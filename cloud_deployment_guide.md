
# 云服务器部署指南

## 1. 选择云服务商
推荐服务商：
- 阿里云 ECS
- 腾讯云 CVM  
- 华为云 ECS
- AWS EC2

## 2. 服务器配置建议
- CPU: 1核心
- 内存: 1GB
- 硬盘: 20GB
- 带宽: 1Mbps
- 操作系统: Ubuntu 20.04 LTS

## 3. 部署步骤

### 3.1 连接服务器
```bash
ssh root@your_server_ip
```

### 3.2 安装Python环境
```bash
apt update
apt install python3 python3-pip -y
```

### 3.3 安装依赖
```bash
pip3 install requests
```

### 3.4 上传代码文件
将以下文件上传到服务器：
- api.py
- ip_resolver.py
- api_client.py

### 3.5 测试运行
```bash
python3 api.py
```

### 3.6 申请新IP白名单
获取服务器IP后，重新申请白名单：
```bash
python3 ip_resolver.py
```

## 4. 自动化部署脚本
```bash
#!/bin/bash
# 一键部署脚本
apt update
apt install python3 python3-pip git -y
pip3 install requests
git clone your_repository_url
cd your_project
python3 api.py
```

## 5. 定时任务设置
如需定时调用API：
```bash
crontab -e
# 每小时执行一次
0 * * * * cd /path/to/project && python3 api.py
```
