import json
import requests
import time
from hashlib import md5

class IPSolutionManager:
    def __init__(self):
        # API配置
        self.product_id = '66dbccbec7a7e3460f5e613f'
        self.secret_id = '5a7847f832736c04d48314fe059ad049'
        self.secret_key = 'e8c31a57ae38aa058dab6c8138928542'
        self.base_url = 'https://console.handaas.com/api/v1/integrator/call_api/6886df3283a317dea1e89f1e'
        
        # 当前IP信息
        self.current_ip = "*************"
        self.local_ip = "***********"
    
    def signature(self, secret_key, params):
        """生成API签名"""
        keys = sorted(list(params.keys()))
        params_str = ''
        for key in keys:
            params_str += str(params[key])
        params_str += secret_key
        return md5(params_str.encode('utf-8')).hexdigest()
    
    def test_api_call(self, proxy_config=None):
        """测试API调用"""
        params = {"matchKeyword": "加油站"}
        
        call_params = {
            'product_id': self.product_id,
            'secret_id': self.secret_id,
            'params': params
        }
        
        sign = self.signature(self.secret_key, call_params)
        call_params["signature"] = sign
        
        try:
            response = requests.post(
                self.base_url, 
                json=call_params, 
                proxies=proxy_config,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result
            else:
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"error": str(e)}
    
    def create_whitelist_application(self):
        """创建详细的白名单申请文档"""
        application = {
            "申请标题": "韶关燃气API IP白名单申请",
            "申请时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "申请人信息": {
                "当前公网IP": self.current_ip,
                "本地网络IP": self.local_ip,
                "网络运营商": "请填写您的网络运营商",
                "地理位置": "请填写您的地理位置"
            },
            "API信息": {
                "产品ID": self.product_id,
                "Secret ID": self.secret_id,
                "API端点": self.base_url
            },
            "申请原因": {
                "业务需求": "韶关燃气数据查询和分析",
                "使用频率": "日常使用，预计每天调用次数",
                "数据用途": "业务数据分析和报表生成"
            },
            "技术信息": {
                "当前错误码": "11028",
                "错误信息": "当前ip不在可用范围内",
                "测试时间": time.strftime("%Y-%m-%d %H:%M:%S"),
                "请求示例": {
                    "product_id": self.product_id,
                    "secret_id": self.secret_id,
                    "params": {"matchKeyword": "加油站"}
                }
            },
            "联系方式": {
                "邮箱": "请填写您的邮箱",
                "电话": "请填写您的电话",
                "紧急联系方式": "请填写紧急联系方式"
            }
        }
        
        # 保存详细申请
        with open('ip_whitelist_application.json', 'w', encoding='utf-8') as f:
            json.dump(application, f, ensure_ascii=False, indent=2)
        
        # 创建简化版本
        simple_request = f"""
IP白名单申请

申请时间: {time.strftime("%Y-%m-%d %H:%M:%S")}
申请IP: {self.current_ip}
产品ID: {self.product_id}
Secret ID: {self.secret_id}
错误信息: 当前ip不在可用范围内 (错误码: 11028)
申请原因: 韶关燃气API数据查询

请将此IP添加到产品白名单中，谢谢！
"""
        
        with open('ip_whitelist_simple.txt', 'w', encoding='utf-8') as f:
            f.write(simple_request)
        
        print("✅ 白名单申请文档已生成:")
        print("  📄 ip_whitelist_application.json (详细版)")
        print("  📝 ip_whitelist_simple.txt (简化版)")
        
        return application
    
    def suggest_temporary_solutions(self):
        """建议临时解决方案"""
        print("\n🔧 临时解决方案建议:")
        print("=" * 50)
        
        solutions = [
            {
                "方案": "1. 云服务器部署 (推荐)",
                "描述": "在云服务器上部署代码，获得固定IP",
                "优势": ["固定IP地址", "高可用性", "易于管理"],
                "云服务商": ["阿里云", "腾讯云", "华为云", "AWS", "Azure"],
                "预估成本": "最低配置约50-100元/月",
                "实施时间": "1-2小时"
            },
            {
                "方案": "2. 代理服务器",
                "描述": "通过已在白名单的代理服务器访问",
                "优势": ["快速实施", "成本较低"],
                "注意事项": ["需要找到可用的代理", "可能影响性能"],
                "实施时间": "30分钟"
            },
            {
                "方案": "3. 移动热点测试",
                "描述": "使用手机热点更换IP地址测试",
                "优势": ["快速验证", "无额外成本"],
                "局限性": ["IP仍可能不在白名单", "仅用于测试"],
                "实施时间": "5分钟"
            }
        ]
        
        for solution in solutions:
            print(f"\n{solution['方案']}")
            print(f"描述: {solution['描述']}")
            print(f"实施时间: {solution['实施时间']}")
            if '优势' in solution:
                print(f"优势: {', '.join(solution['优势'])}")
            if '预估成本' in solution:
                print(f"预估成本: {solution['预估成本']}")
    
    def create_cloud_deployment_guide(self):
        """创建云部署指南"""
        guide = """
# 云服务器部署指南

## 1. 选择云服务商
推荐服务商：
- 阿里云 ECS
- 腾讯云 CVM  
- 华为云 ECS
- AWS EC2

## 2. 服务器配置建议
- CPU: 1核心
- 内存: 1GB
- 硬盘: 20GB
- 带宽: 1Mbps
- 操作系统: Ubuntu 20.04 LTS

## 3. 部署步骤

### 3.1 连接服务器
```bash
ssh root@your_server_ip
```

### 3.2 安装Python环境
```bash
apt update
apt install python3 python3-pip -y
```

### 3.3 安装依赖
```bash
pip3 install requests
```

### 3.4 上传代码文件
将以下文件上传到服务器：
- api.py
- ip_resolver.py
- api_client.py

### 3.5 测试运行
```bash
python3 api.py
```

### 3.6 申请新IP白名单
获取服务器IP后，重新申请白名单：
```bash
python3 ip_resolver.py
```

## 4. 自动化部署脚本
```bash
#!/bin/bash
# 一键部署脚本
apt update
apt install python3 python3-pip git -y
pip3 install requests
git clone your_repository_url
cd your_project
python3 api.py
```

## 5. 定时任务设置
如需定时调用API：
```bash
crontab -e
# 每小时执行一次
0 * * * * cd /path/to/project && python3 api.py
```
"""
        
        with open('cloud_deployment_guide.md', 'w', encoding='utf-8') as f:
            f.write(guide)
        
        print("✅ 云部署指南已生成: cloud_deployment_guide.md")
    
    def check_alternative_endpoints(self):
        """检查是否有其他可用的API端点"""
        print("\n🔍 检查替代方案...")
        
        # 可能的替代端点（示例）
        alternative_endpoints = [
            "https://api.handaas.com/v1/integrator/call_api/6886df3283a317dea1e89f1e",
            "https://console.handaas.com/api/v2/integrator/call_api/6886df3283a317dea1e89f1e",
        ]
        
        print("💡 建议联系API提供方确认:")
        print("1. 是否有其他可用的API端点")
        print("2. 是否有测试环境可以使用")
        print("3. IP白名单的申请流程和时间")
        print("4. 是否支持动态IP或IP段白名单")

def main():
    print("🚀 IP限制问题完整解决方案")
    print("=" * 50)
    
    manager = IPSolutionManager()
    
    # 1. 测试当前状态
    print("1️⃣ 测试当前API状态:")
    result = manager.test_api_call()
    if result.get('code') == '11028':
        print("❌ 确认IP限制问题存在")
    else:
        print("✅ API状态正常")
        return
    
    # 2. 生成白名单申请
    print(f"\n2️⃣ 生成白名单申请文档:")
    manager.create_whitelist_application()
    
    # 3. 提供临时解决方案
    manager.suggest_temporary_solutions()
    
    # 4. 生成部署指南
    print(f"\n3️⃣ 生成云部署指南:")
    manager.create_cloud_deployment_guide()
    
    # 5. 检查替代方案
    manager.check_alternative_endpoints()
    
    print(f"\n" + "=" * 50)
    print("📋 总结和下一步:")
    print("1. 📧 发送白名单申请给API管理员")
    print("2. ☁️  考虑云服务器部署 (推荐)")
    print("3. 📞 联系API提供方了解详细流程")
    print("4. 🔄 定期测试API状态")
    
    print(f"\n📁 生成的文件:")
    print("- ip_whitelist_application.json (详细申请)")
    print("- ip_whitelist_simple.txt (简化申请)")
    print("- cloud_deployment_guide.md (部署指南)")

if __name__ == '__main__':
    main()
