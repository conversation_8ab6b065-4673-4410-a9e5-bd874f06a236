import json
import requests
import time
from hashlib import md5
from datetime import datetime

class IPTestMonitor:
    def __init__(self):
        # API配置
        self.product_id = '66dbccbec7a7e3460f5e613f'
        self.secret_id = '5a7847f832736c04d48314fe059ad049'
        self.secret_key = 'e8c31a57ae38aa058dab6c8138928542'
        self.base_url = 'https://console.handaas.com/api/v1/integrator/call_api/6886df3283a317dea1e89f1e'
        
        # 测试记录
        self.test_log = []
    
    def signature(self, secret_key, params):
        """生成API签名"""
        keys = sorted(list(params.keys()))
        params_str = ''
        for key in keys:
            params_str += str(params[key])
        params_str += secret_key
        return md5(params_str.encode('utf-8')).hexdigest()
    
    def quick_test(self):
        """快速测试API状态"""
        params = {"matchKeyword": "加油站"}
        
        call_params = {
            'product_id': self.product_id,
            'secret_id': self.secret_id,
            'params': params
        }
        
        sign = self.signature(self.secret_key, call_params)
        call_params["signature"] = sign
        
        test_time = datetime.now()
        
        try:
            response = requests.post(self.base_url, json=call_params, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code')
                msg = result.get('msgCN', '')
                
                # 记录测试结果
                test_record = {
                    'time': test_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'status': 'success' if code == '0' else 'failed',
                    'code': code,
                    'message': msg,
                    'response_time': (datetime.now() - test_time).total_seconds()
                }
                
                self.test_log.append(test_record)
                
                if code == '0':
                    print(f"✅ {test_time.strftime('%H:%M:%S')} - API调用成功!")
                    data_count = len(result.get('data', []))
                    print(f"   📊 返回数据: {data_count} 条")
                    return True
                elif code == '11028':
                    print(f"❌ {test_time.strftime('%H:%M:%S')} - IP仍未加入白名单")
                    return False
                else:
                    print(f"⚠️  {test_time.strftime('%H:%M:%S')} - 其他错误: {msg} (代码: {code})")
                    return False
            else:
                print(f"❌ {test_time.strftime('%H:%M:%S')} - HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ {test_time.strftime('%H:%M:%S')} - 请求异常: {e}")
            return False
    
    def continuous_monitor(self, interval=300, max_tests=20):
        """持续监控API状态"""
        print(f"🔄 开始持续监控 (间隔: {interval}秒, 最大测试次数: {max_tests})")
        print("=" * 60)
        
        for i in range(max_tests):
            print(f"\n第 {i+1}/{max_tests} 次测试:")
            
            success = self.quick_test()
            
            if success:
                print("🎉 IP白名单已生效! 监控结束.")
                self.save_test_log()
                return True
            
            if i < max_tests - 1:  # 不是最后一次测试
                print(f"⏰ 等待 {interval} 秒后进行下次测试...")
                time.sleep(interval)
        
        print(f"\n⏰ 监控结束，共测试 {max_tests} 次")
        self.save_test_log()
        return False
    
    def save_test_log(self):
        """保存测试日志"""
        log_data = {
            'test_summary': {
                'total_tests': len(self.test_log),
                'successful_tests': len([t for t in self.test_log if t['status'] == 'success']),
                'failed_tests': len([t for t in self.test_log if t['status'] == 'failed']),
                'first_test': self.test_log[0]['time'] if self.test_log else None,
                'last_test': self.test_log[-1]['time'] if self.test_log else None
            },
            'test_details': self.test_log
        }
        
        filename = f"ip_test_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, ensure_ascii=False, indent=2)
        
        print(f"📝 测试日志已保存: {filename}")
    
    def show_status_summary(self):
        """显示状态摘要"""
        if not self.test_log:
            print("📊 暂无测试记录")
            return
        
        total = len(self.test_log)
        success = len([t for t in self.test_log if t['status'] == 'success'])
        failed = total - success
        
        print(f"\n📊 测试摘要:")
        print(f"   总测试次数: {total}")
        print(f"   成功次数: {success}")
        print(f"   失败次数: {failed}")
        print(f"   成功率: {(success/total*100):.1f}%")
        
        if self.test_log:
            latest = self.test_log[-1]
            print(f"   最新状态: {latest['status']} ({latest['time']})")
    
    def manual_test_mode(self):
        """手动测试模式"""
        print("🔧 手动测试模式")
        print("输入命令: test (测试), monitor (监控), status (状态), quit (退出)")
        
        while True:
            command = input("\n请输入命令: ").strip().lower()
            
            if command == 'test':
                self.quick_test()
            elif command == 'monitor':
                interval = input("监控间隔(秒，默认300): ").strip()
                interval = int(interval) if interval.isdigit() else 300
                max_tests = input("最大测试次数(默认20): ").strip()
                max_tests = int(max_tests) if max_tests.isdigit() else 20
                self.continuous_monitor(interval, max_tests)
            elif command == 'status':
                self.show_status_summary()
            elif command == 'quit':
                print("👋 退出监控")
                break
            else:
                print("❌ 无效命令")

def main():
    print("🚀 IP白名单状态监控工具")
    print("=" * 50)
    
    monitor = IPTestMonitor()
    
    print("选择运行模式:")
    print("1. 快速测试 (单次)")
    print("2. 持续监控 (自动)")
    print("3. 手动模式 (交互)")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        print("\n🔍 执行快速测试:")
        success = monitor.quick_test()
        if success:
            print("\n🎉 恭喜! IP白名单已生效，API可以正常使用!")
        else:
            print("\n⏰ IP白名单尚未生效，请稍后再试或联系管理员")
    
    elif choice == '2':
        print("\n🔄 启动持续监控:")
        interval = input("监控间隔(秒，默认300): ").strip()
        interval = int(interval) if interval.isdigit() else 300
        
        max_tests = input("最大测试次数(默认20): ").strip()
        max_tests = int(max_tests) if max_tests.isdigit() else 20
        
        monitor.continuous_monitor(interval, max_tests)
    
    elif choice == '3':
        monitor.manual_test_mode()
    
    else:
        print("❌ 无效选择")
    
    # 显示最终状态
    monitor.show_status_summary()

if __name__ == '__main__':
    main()
