import json
import requests
import socket
from hashlib import md5
import time

class IPResolver:
    def __init__(self):
        # 从api.py获取最新配置
        self.product_id = '66dbccbec7a7e3460f5e613f'
        self.secret_id = '5a7847f832736c04d48314fe059ad049'
        self.secret_key = 'e8c31a57ae38aa058dab6c8138928542'
        self.base_url = 'https://console.handaas.com/api/v1/integrator/call_api/6886df3283a317dea1e89f1e'
        
    def get_current_ip(self):
        """获取当前公网IP地址"""
        ip_services = [
            'https://api.ipify.org',
            'https://ipinfo.io/ip',
            'https://api.ip.sb/ip',
            'https://ifconfig.me/ip',
            'https://icanhazip.com'
        ]
        
        print("🔍 正在获取当前公网IP地址...")
        
        for service in ip_services:
            try:
                response = requests.get(service, timeout=5)
                if response.status_code == 200:
                    ip = response.text.strip()
                    print(f"✅ 当前公网IP: {ip}")
                    return ip
            except Exception as e:
                print(f"❌ {service} 获取失败: {e}")
                continue
        
        print("❌ 无法获取公网IP地址")
        return None
    
    def get_local_ip(self):
        """获取本地IP地址"""
        try:
            # 连接到一个远程地址来获取本地IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            print(f"📍 本地IP地址: {local_ip}")
            return local_ip
        except Exception as e:
            print(f"❌ 获取本地IP失败: {e}")
            return None
    
    def signature(self, secret_key, params):
        """生成API签名"""
        keys = sorted(list(params.keys()))
        params_str = ''
        for key in keys:
            params_str += str(params[key])
        params_str += secret_key
        return md5(params_str.encode('utf-8')).hexdigest()
    
    def test_api_with_proxy(self, proxy_config=None):
        """使用代理测试API"""
        params = {"matchKeyword": "加油站"}
        
        call_params = {
            'product_id': self.product_id,
            'secret_id': self.secret_id,
            'params': params
        }
        
        sign = self.signature(self.secret_key, call_params)
        call_params["signature"] = sign
        
        proxies = proxy_config if proxy_config else None
        
        try:
            if proxies:
                print(f"🔄 使用代理测试: {proxies}")
            else:
                print("🔄 直连测试...")
                
            response = requests.post(
                self.base_url, 
                json=call_params, 
                proxies=proxies,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                code = result.get('code')
                msg = result.get('msgCN', '')
                
                print(f"📊 响应码: {code}")
                print(f"📝 响应消息: {msg}")
                
                if code == '0':
                    print("✅ API调用成功!")
                    return True
                elif code == '11028':
                    print("❌ IP仍然不在可用范围内")
                    return False
                else:
                    print(f"❌ 其他错误: {msg}")
                    return False
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False
    
    def generate_ip_whitelist_request(self):
        """生成IP白名单申请信息"""
        current_ip = self.get_current_ip()
        local_ip = self.get_local_ip()
        
        print("\n" + "=" * 50)
        print("📋 IP白名单申请信息")
        print("=" * 50)
        
        request_info = {
            "申请时间": time.strftime("%Y-%m-%d %H:%M:%S"),
            "当前公网IP": current_ip,
            "本地IP": local_ip,
            "产品ID": self.product_id,
            "Secret ID": self.secret_id,
            "申请原因": "API调用需要",
            "使用场景": "韶关燃气数据查询"
        }
        
        print("请将以下信息发送给API管理员:")
        print("-" * 30)
        for key, value in request_info.items():
            print(f"{key}: {value}")
        
        # 保存到文件
        with open('ip_whitelist_request.json', 'w', encoding='utf-8') as f:
            json.dump(request_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 申请信息已保存到: ip_whitelist_request.json")
        return request_info
    
    def suggest_solutions(self):
        """提供解决方案建议"""
        print("\n" + "=" * 50)
        print("🔧 IP限制解决方案")
        print("=" * 50)
        
        solutions = [
            {
                "方案": "1. 申请IP白名单",
                "描述": "联系API管理员将您的IP添加到白名单",
                "优先级": "⭐⭐⭐⭐⭐",
                "实施难度": "简单",
                "操作": "运行 generate_ip_whitelist_request() 生成申请信息"
            },
            {
                "方案": "2. 使用固定IP服务器",
                "描述": "部署到有固定IP的服务器上",
                "优先级": "⭐⭐⭐⭐",
                "实施难度": "中等",
                "操作": "购买云服务器或VPS，部署代码"
            },
            {
                "方案": "3. 使用代理服务",
                "描述": "通过白名单内的代理服务器访问",
                "优先级": "⭐⭐⭐",
                "实施难度": "中等",
                "操作": "配置HTTP/HTTPS代理"
            },
            {
                "方案": "4. VPN解决方案",
                "描述": "使用企业VPN获取固定出口IP",
                "优先级": "⭐⭐",
                "实施难度": "复杂",
                "操作": "配置企业级VPN服务"
            }
        ]
        
        for i, solution in enumerate(solutions, 1):
            print(f"\n{solution['方案']}")
            print(f"   描述: {solution['描述']}")
            print(f"   优先级: {solution['优先级']}")
            print(f"   实施难度: {solution['实施难度']}")
            print(f"   操作: {solution['操作']}")
    
    def test_proxy_configs(self):
        """测试常见代理配置"""
        print("\n🔄 测试代理配置...")
        
        # 常见代理配置示例
        proxy_configs = [
            None,  # 直连
            {
                'http': 'http://proxy.example.com:8080',
                'https': 'https://proxy.example.com:8080'
            },
            # 可以添加更多代理配置
        ]
        
        for i, proxy in enumerate(proxy_configs):
            print(f"\n测试配置 {i+1}:")
            success = self.test_api_with_proxy(proxy)
            if success:
                print("✅ 找到可用配置!")
                return proxy
        
        print("❌ 所有配置都无法绕过IP限制")
        return None

def main():
    print("🚀 IP限制问题解决工具")
    print("=" * 50)
    
    resolver = IPResolver()
    
    # 获取当前IP信息
    print("1️⃣ 获取IP信息:")
    resolver.get_current_ip()
    resolver.get_local_ip()
    
    # 测试当前API状态
    print(f"\n2️⃣ 测试当前API状态:")
    resolver.test_api_with_proxy()
    
    # 生成白名单申请
    print(f"\n3️⃣ 生成白名单申请:")
    resolver.generate_ip_whitelist_request()
    
    # 提供解决方案
    resolver.suggest_solutions()
    
    print(f"\n" + "=" * 50)
    print("📞 下一步操作:")
    print("1. 将 ip_whitelist_request.json 发送给API管理员")
    print("2. 等待IP白名单配置完成")
    print("3. 重新测试API调用")
    print("4. 如需紧急使用，考虑使用固定IP服务器")

if __name__ == '__main__':
    main()
