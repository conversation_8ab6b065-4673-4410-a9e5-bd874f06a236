import json
from hashlib import md5

import requests

def call_api_demo():
    # 数据产品id
    product_id = '66dbccbec7a7e3460f5e613f'
    # accessToken 每个对接器唯一
    secret_id = 'd0589267ee0e5c9a949d781cbb4e0e6b'
    # 秘钥 每个对接器唯一
    secret_key = '2adce9ee75e1a147f8507dfc614db839'

    # 查询所有数据，支持分页
    all_data = []
    page = 1
    page_size = 100  # 每页数量

    while True:
        # 查询数据产品的入参,json字符串格式
        params = {
            "matchKeyword": "加油站",
            "page": page,
            "pageSize": page_size
        }

        call_params = {
            'product_id': product_id,
            'secret_id': secret_id,
            'params': params
        }
        sign = signature(secret_key, call_params)
        call_params["signature"] = sign

        print(f"=== 第{page}页请求参数 ===")
        print(json.dumps(call_params, ensure_ascii=False, indent=4))

        url = 'https://console.handaas.com/api/v1/integrator/call_api/688721aa83a317dea1e8a064'
        rsp = requests.post(url, json=call_params)

        response_json = rsp.json()
        print(f"\n=== 第{page}页API响应 ===")
        print(json.dumps(response_json, ensure_ascii=False, indent=4))

        # 检查响应状态
        if response_json.get('code') != '10000':
            print(f"API调用失败: {response_json.get('msgCN', '未知错误')}")
            break

        # 获取数据
        data = response_json.get('data')
        if data is None:
            print("没有更多数据")
            break

        # 处理数据格式 - 可能是单个对象或数组
        if isinstance(data, list):
            if len(data) == 0:
                print("没有更多数据")
                break
            all_data.extend(data)
            print(f"第{page}页获取到 {len(data)} 条数据")

            # 如果返回的数据少于页面大小，说明已经是最后一页
            if len(data) < page_size:
                break
        else:
            # 单个对象
            all_data.append(data)
            print(f"第{page}页获取到 1 条数据")
            break  # 单个对象通常表示只有一条数据

        page += 1

        # 防止无限循环，最多查询10页
        if page > 10:
            print("已查询10页，停止查询")
            break

    # 输出汇总信息
    print(f"\n=== 数据汇总 ===")
    print(f"总共获取到 {len(all_data)} 条数据")

    # 输出所有数据
    print(f"\n=== 所有数据详情 ===")
    for i, item in enumerate(all_data, 1):
        print(f"\n--- 第{i}条数据 ---")
        print(json.dumps(item, ensure_ascii=False, indent=4))

def signature(secret_key, params):
    keys = sorted(list(params.keys()))
    params_str = ''
    for key in keys:
        params_str += str(params[key])
    params_str += secret_key
    return md5(params_str.encode('utf-8')).hexdigest()

if __name__ == '__main__':
    call_api_demo()