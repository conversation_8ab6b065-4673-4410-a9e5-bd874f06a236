import json
from hashlib import md5

import requests

def call_api_demo():
    # 数据产品id
    product_id = '6886eb88e4d471aeaee10a43'
    # accessToken 每个对接器唯一
    secret_id = 'dcbf37668bd802dc24bef31cbddc02ff'
    # 秘钥 每个对接器唯一
    secret_key = '6706ef811d2761dad0c0d78437972287'
    # 查询数据产品的入参,json字符串格式
    params = {}

    call_params = {
        'product_id': product_id,
        'secret_id': secret_id,
        'params': params
    }
    sign = signature(secret_key, call_params)
    call_params["signature"] = sign

    print("=== API调用参数 ===")
    print(json.dumps(call_params, ensure_ascii=False, indent=2))

    url = 'https://console.handaas.com/api/v1/integrator/call_api/6886eb875418b96a4bf50dcd'
    print(f"\n=== 请求URL ===")
    print(url)

    try:
        print("\n=== 发送请求 ===")
        rsp = requests.post(url, json=call_params, timeout=30)
        print(f"HTTP状态码: {rsp.status_code}")

        if rsp.status_code == 200:
            resp_json = rsp.json()
            print("\n=== API响应 ===")
            print(json.dumps(resp_json, ensure_ascii=False, indent=2))

            # 检查响应状态
            if resp_json.get('code') == '11014':
                print("\n❌ 错误: 产品不存在")
                print("可能的原因:")
                print("1. product_id 不正确")
                print("2. 产品已被删除或停用")
                print("3. 没有访问该产品的权限")
                return False
            elif resp_json.get('code') and resp_json.get('code') != '0':
                print(f"\n❌ API返回错误: {resp_json.get('msgCN', '未知错误')}")
                return False
            else:
                print("\n✅ API调用成功!")
                return True
        else:
            print(f"\n❌ HTTP请求失败: {rsp.status_code}")
            print(f"响应内容: {rsp.text}")
            return False

    except requests.exceptions.Timeout:
        print("\n❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("\n❌ 连接错误，请检查网络")
        return False
    except Exception as e:
        print(f"\n❌ 请求异常: {str(e)}")
        return False

def signature(secret_key, params):
    print("params", params)
    keys = sorted(list(params.keys()))
    print("keys", keys)
    params_str = ''
    for key in keys:
        params_str += str(params[key])
    params_str += secret_key
    return md5(params_str.encode('utf-8')).hexdigest()

if __name__ == '__main__':
    call_api_demo()