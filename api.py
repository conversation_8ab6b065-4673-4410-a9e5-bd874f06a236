import json
from hashlib import md5

import requests

def call_api_demo():
    # 数据产品id
    product_id = '6886eb88e4d471aeaee10a43'
    # accessToken 每个对接器唯一
    secret_id = 'dcbf37668bd802dc24bef31cbddc02ff'
    # 秘钥 每个对接器唯一
    secret_key = '6706ef811d2761dad0c0d78437972287'
    # 查询数据产品的入参,json字符串格式
    params = {}

    call_params = {
        'product_id': product_id,
        'secret_id': secret_id,
        'params': params
    }
    sign = signature(secret_key, call_params)
    call_params["signature"] = sign
    print(call_params)
    url = 'https://console.handaas.com/api/v1/integrator/call_api/6886eb875418b96a4bf50dcd'
    rsp = requests.post(url, json=call_params)
    resp_data = json.dumps(rsp.json(), ensure_ascii=False)
    print(resp_data)

def signature(secret_key, params):
    print("params", params)
    keys = sorted(list(params.keys()))
    print("keys", keys)
    params_str = ''
    for key in keys:
        params_str += str(params[key])
    params_str += secret_key
    return md5(params_str.encode('utf-8')).hexdigest()

if __name__ == '__main__':
    call_api_demo()